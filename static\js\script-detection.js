// 复制主页面的检测函数
function checkScriptMarker() {
    return window.userScriptDetected ||
           (typeof window.OriginalWebSocket !== 'undefined' && !window.inlineScriptActive) ||
           (window.WebSocket && window.WebSocket.toString().includes('Modified')) ||
           (window.WebSocket && window.WebSocket.name === 'WebSocket' && window.WebSocket.toString().includes('modifiedUrl'));
}

function checkWebSocketModification() {
    if (!window.WebSocket) return false;

    try {
        if (window.inlineScriptActive) {
            return false;
        }

        const wsString = window.WebSocket.toString();

        if (wsString.includes('wss://') && wsString.includes('ws://') &&
            (wsString.includes('replace') || wsString.includes('modifiedUrl'))) {
            return true;
        }

        if (window.OriginalWebSocket && !window.inlineScriptActive) {
            return true;
        }

        if (window.WebSocket.prototype && window.WebSocket.prototype.constructor !== window.WebSocket) {
            return true;
        }

        return false;
    } catch (e) {
        return false;
    }
}

function checkUserScript() {
    const hasScript = checkScriptMarker();
    const wsModified = checkWebSocketModification();
    const hasStorageMarker = localStorage.getItem('userscript_delta_map_tool') === 'installed';
    const scriptInstalled = hasScript || wsModified || hasStorageMarker;
    return scriptInstalled;
}

function tryInlineWebSocketFix() {
    if (window.WebSocket && !window.OriginalWebSocket) {
        try {
            window.OriginalWebSocket = window.WebSocket;
            window.WebSocket = function(url, protocols) {
                const modifiedUrl = url.replace(/^wss:\/\//i, 'ws://');
                console.log(`[内嵌脚本] WebSocket URL修改: ${url} -> ${modifiedUrl}`);
                return new window.OriginalWebSocket(modifiedUrl, protocols);
            };
            Object.setPrototypeOf(window.WebSocket, window.OriginalWebSocket);
            window.WebSocket.prototype = window.OriginalWebSocket.prototype;
            console.log('[内嵌脚本] WebSocket修改功能已激活');
            return true;
        } catch (e) {
            console.error('[内嵌脚本] 无法修改WebSocket:', e);
            return false;
        }
    }
    return false;
}

function addResult(message, type = 'info') {
    const results = document.getElementById('results');
    const div = document.createElement('div');
    div.className = `test-result ${type}`;
    div.innerHTML = message;
    results.appendChild(div);
}

function clearResults() {
    document.getElementById('results').innerHTML = '';
}

function runAllTests() {
    clearResults();
    addResult('<h3>🧪 开始检测测试</h3>', 'info');

    // 测试1: 检查初始状态
    addResult(`<strong>测试1: 初始状态检查</strong><br>
              - window.OriginalWebSocket 存在: ${typeof window.OriginalWebSocket !== 'undefined'}<br>
              - window.inlineScriptActive: ${window.inlineScriptActive}<br>
              - localStorage 标记: ${localStorage.getItem('userscript_delta_map_tool')}<br>
              - window.userScriptDetected: ${window.userScriptDetected}`, 'info');

    // 测试2: 检测函数结果
    const hasScript = checkScriptMarker();
    const wsModified = checkWebSocketModification();
    const userScript = checkUserScript();
    
    addResult(`<strong>测试2: 检测函数结果</strong><br>
              - checkScriptMarker(): ${hasScript}<br>
              - checkWebSocketModification(): ${wsModified}<br>
              - checkUserScript(): ${userScript}`, hasScript || wsModified || userScript ? 'success' : 'warning');

    // 测试3: WebSocket 状态
    const wsString = window.WebSocket.toString().substring(0, 100);
    addResult(`<strong>测试3: WebSocket 状态</strong><br>
              <div class="code">${wsString}...</div>`, 'info');

    // 测试4: 综合判断
    const hasRealUserScript = checkUserScript();
    const hasInlineScript = window.inlineScriptActive === true;
    
    let finalStatus = '';
    if (hasRealUserScript) {
        finalStatus = '✅ 检测到真正的油猴脚本';
        addResult(`<strong>最终结果:</strong> ${finalStatus}`, 'success');
    } else if (hasInlineScript) {
        finalStatus = '⚡ 只有内嵌脚本激活';
        addResult(`<strong>最终结果:</strong> ${finalStatus}`, 'warning');
    } else {
        finalStatus = '❌ 没有检测到任何脚本';
        addResult(`<strong>最终结果:</strong> ${finalStatus}`, 'error');
    }
}

function simulateUserScript() {
    // 模拟油猴脚本的存在
    window.userScriptDetected = true;
    localStorage.setItem('userscript_delta_map_tool', 'installed');
    addResult('🐒 已模拟油猴脚本安装，请重新运行测试', 'success');
}

function resetEnvironment() {
    // 重置环境
    delete window.userScriptDetected;
    delete window.OriginalWebSocket;
    delete window.inlineScriptActive;
    localStorage.removeItem('userscript_delta_map_tool');
    
    // 重新加载页面以完全重置
    location.reload();
}

// 页面加载时自动运行测试
window.onload = function() {
    // 模拟主页面的初始化逻辑
    const hasRealUserScript = checkScriptMarker() ||
                            (window.OriginalWebSocket && !window.inlineScriptActive) ||
                            localStorage.getItem('userscript_delta_map_tool') === 'installed';

    if (!hasRealUserScript) {
        const success = tryInlineWebSocketFix();
        if (success) {
            window.inlineScriptActive = true;
        }
    }

    setTimeout(runAllTests, 1000);
};

// 导出函数供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        checkScriptMarker,
        checkWebSocketModification,
        checkUserScript,
        tryInlineWebSocketFix
    };
}
